.bgTable {
  .headBox {
    padding-bottom: 9.5px;
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 23.5px;

    :deep(.sw-box) {
      .title {
        color: var(--color-bk2);
        width: 64px;
        font-size: 14px;
        display: inline-block;
        margin-right: 30px;
        //  文字左右居中
        text-align: center;
      }

      .line {
        color: var(--color-main);
        font-weight: 500;
      }
    }
  }

  .formBox {

    .btn-search,
    .btn-reset {
      width: 60px;
      border-radius: 3px;
      height: 32px;
    }
  }

  .tableBox {
    padding: 24px 32px;
    height: 136px;
    margin-bottom: 24px;
    border-bottom: 1px solid #e8e8e8;
  }

  .t-col-2 {
    display: flex;
  }

  .baseList {
    padding-top: 20px;
    min-height: 674px;

    .newBox {
      margin-bottom: 16px;
      display: flex;

      .newBoxbutton {
        // 右对齐
        margin-left: auto;
        width: 88px;
      }
    }

    .name {
      img {
        width: 32px;
        height: 32px;
        vertical-align: bottom;
        border-radius: 4px;
      }

      display: flex;
      align-items: center;

      span {
        display: contents;
      }
    }

    .updateTime {
      font-family: HelveticaNeue;
    }



    .btn-search {
      margin-left: 9px;
    }

    .line {
      padding: 0 7.5px;
      // 在框线外一边扩展7.5px
    }

    .btn-split-right {
      padding-right: 7.5px;
    }

    .btn-split-left {
      padding-left: 7.5px;
    }

    .tdesign-demo-image-viewer__ui-image {
      width: 39px;
      height: 39px;
      display: inline-flex;
      position: relative;
      justify-content: center;
      align-items: center;
      border-radius: var(--td-radius-small);
      overflow: hidden;
      margin-right: 10px;
    }

    .tdesign-demo-image-viewer__ui-image--hover {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      left: 0;
      top: 0;
      opacity: 0;
      background-color: rgba(0, 0, 0, 0.6);
      color: var(--td-text-color-anti);
      line-height: 22px;
      transition: 0.2s;
    }

    .tdesign-demo-image-viewer__ui-image:hover .tdesign-demo-image-viewer__ui-image--hover {
      opacity: 1;
      cursor: pointer;
    }

    .tdesign-demo-image-viewer__ui-image--img {
      width: auto;
      height: auto;
      max-width: 32px;
      max-width: 32px;
      cursor: pointer;
      position: absolute;
    }

    .tdesign-demo-image-viewer__ui-image--footer {
      padding: 0 16px;
      height: 56px;
      width: 100%;
      line-height: 56px;
      font-size: 16px;
      position: absolute;
      bottom: 0;
      color: var(--td-text-color-anti);
      background-image: linear-gradient(0deg,
          rgba(0, 0, 0, 0.4) 0%,
          rgba(0, 0, 0, 0) 100%);
      display: flex;
      box-sizing: border-box;
    }

    .tdesign-demo-image-viewer__ui-image--title {
      flex: 1;
    }

    .tdesign-demo-popup__reference {
      margin-left: 16px;
    }

    .tdesign-demo-image-viewer__ui-image--icons .tdesign-demo-icon {
      cursor: pointer;
    }

    .tdesign-demo-image-viewer__base {
      width: 160px;
      height: 160px;
      margin: 10px;
      border: 4px solid var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-medium);
    }

    :deep(.t-table td) {
      height: 50px;
      padding-top: 0;
      padding-bottom: 0;
    }

    .descriptionheight {
      margin-top: 10px;
      margin-bottom: 10px;
    }

    .description {
      // 自动折行，超出两行显示省略号
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      width: 252px;

      .hover {
        display: none;
        position: absolute;
        // 位于div上方中间位置
        top: 61px;
        width: 334px;
        max-height: 163px;
        font-size: 12px;
        background-color: #fff;
        line-height: 17px;
        box-shadow: 0 0 8px 1px rgba(34, 40, 51, 0.12);
        border-radius: 4px;
        padding: 15px;
        color: var(--color-bk1);
        word-break: break-all;
        z-index: 100;

        &::after {
          content: '';
          position: absolute;
          top: -5px;
          left: 5%;
          width: 10px;
          height: 10px;
          transform: rotate(45deg);
          background-color: #fff;
          // box-shadow: 0 0 8px 1px rgba(0, 0, 0, 0.05);
        }
      }

      &:hover {
        .hover {
          display: block;
        }
      }
    }

    :deep(.t-table__body) {
      tr {

        &:nth-last-child(3) &:nth-last-child(2),
        &:last-child {
          .hover {
            top: auto;
            bottom: 63px;
            ;

            &::after {
              top: auto;
              bottom: -5px;
            }
          }
        }
      }
    }
  }

  .t-tree__line::before {
    height: 37px;
  }
}

.citySelect {
  height: 32px;

  :deep(.t-form__controls) {
    background: #F74346;
    float: right;
    border-radius: 2px;

    .t-input:hover {
      border-color: transparent;
    }

    .t-input--focused {
      box-shadow: none;
    }

    .t-input.t-is-readonly {
      width: 100px;
      background: var(--color-main);
      color: #fff;

      .t-input__inner {
        color: #fff;

        &::placeholder {
          color: #fff;
        }
      }
    }

    .t-select .t-fake-arrow {
      color: #fff;
    }
  }
}