{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "lib": ["esnext", "dom"], "types": ["vite/client"], "noEmit": true, "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["**/*.ts", "src/**/*.d.ts", "src/types/**/*.d.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "compileOnSave": false}