import Layout from '@/layouts/index.vue'
// import LogoutIcon from '@/assets/test-img/assets-slide-logout.svg'
// import PersonIcon from '@/assets/test-img/icon_menu_diaodu.svg'
// import GrzxIcon from '@/assets/test-img/icon_menu_grzx.svg'
import HomeIcon from '@/assets/test-img/icon_menu_diaodu.svg'
import ModelIcon from '@/assets/test-img/icon_menu_zj.svg'

export default [
  // {
  //   path: '/defaults',
  //   name: 'defaults',
  //   component: Layout,
  //   redirect: '/defaults/serviceError',
  //   meta: {
  //     title: '缺省页',
  //     icon: HomeIcon
  //   },
  //   children: [
  //     {
  //       path: 'serviceError',
  //       name: 'serviceErrorPage',
  //       component: () => import('@/pages/default/serviceError.vue'),
  //       meta: {
  //         title: '服务器出错页'
  //       }
  //     },
  //     {
  //       path: 'netWorkAnomaly',
  //       name: 'netWorkAnomalyPage',
  //       component: () => import('@/pages/default/netWorkAnomaly.vue'),
  //       meta: {
  //         title: '网络异常页'
  //       }
  //     },
  //     {
  //       path: 'netWorkDisconnection',
  //       name: 'netWorkDisconnectionPage',
  //       component: () => import('@/pages/default/netWorkDisconnection.vue'),
  //       meta: {
  //         title: '网络断开页'
  //       }
  //     },
  //     {
  //       path: '404',
  //       name: '404Page',
  //       component: () => import('@/pages/default/404.vue'),
  //       meta: {
  //         title: '访问页面不存在'
  //       }
  //     },
  //     {
  //       path: 'forbidden',
  //       name: 'forbiddenPage',
  //       component: () => import('@/pages/default/forbidden.vue'),
  //       meta: {
  //         title: '无权限'
  //       }
  //     }
  //   ]
  // },

  // {
  //   path: '/module',
  //   name: 'module',
  //   component: Layout,
  //   redirect: '/module/moduleList',
  //   meta: {
  //     title: '组件',
  //     icon: ModelIcon
  //   },
  //   children: [
  //     {
  //       path: 'moduleList',
  //       name: 'moduleList',
  //       component: () => import('@/pages/module/list.vue'),
  //       meta: {
  //         title: '列表',
  //         singles: true
  //       }
  //     },
  //     // {
  //     //   path: 'oneList',
  //     //   name: 'oneList',
  //     //   component: () => import('@/pages/module/list1.vue'),
  //     //   meta: {
  //     //     title: '列表(可展开1)',
  //     //     singles: true
  //     //   }
  //     // },
  //     // {
  //     //   path: 'twoList',
  //     //   name: 'twoList',
  //     //   component: () => import('@/pages/module/list2.vue'),
  //     //   meta: {
  //     //     title: '列表(可展开2)',
  //     //     singles: true
  //     //   }
  //     // },
  //     // {
  //     //   path: 'tabList',
  //     //   name: 'tabList',
  //     //   component: () => import('@/pages/module/listTab.vue'),
  //     //   meta: {
  //     //     title: '列表(带Tab)',
  //     //     singles: true
  //     //   }
  //     // },
  //     // {
  //     //   path: 'treeList',
  //     //   name: 'treeList',
  //     //   component: () => import('@/pages/module/listTree.vue'),
  //     //   meta: {
  //     //     title: '列表(树状)',
  //     //     singles: true
  //     //   }
  //     // },
  //     {
  //       path: 'popup',
  //       name: 'popup',
  //       component: () => import('@/pages/module/popup.vue'),
  //       meta: {
  //         title: '弹窗',
  //         singles: true
  //       }
  //     },
  //     // {
  //     //   path: 'popupTab',
  //     //   name: 'popupTab',
  //     //   component: () => import('@/pages/module/popupTab.vue'),
  //     //   meta: {
  //     //     title: '弹窗(带tab)',
  //     //     singles: true
  //     //   }
  //     // },
  //     // {
  //     //   path: 'popupFrom',
  //     //   name: 'popupFrom',
  //     //   component: () => import('@/pages/module/popupFrom.vue'),
  //     //   meta: {
  //     //     title: '弹窗(带表单)',
  //     //     singles: true
  //     //   }
  //     // },
  //     {
  //       path: 'forbiddenTip',
  //       name: 'forbiddenTip',
  //       component: () => import('@/pages/module/forbiddenTip.vue'),
  //       meta: {
  //         title: '禁用提示',
  //         singles: true
  //       }
  //     },
  //     {
  //       path: 'addData',
  //       name: 'addData',
  //       component: () => import('@/pages/module/addData.vue'),
  //       meta: {
  //         title: '动态增加数据',
  //         singles: true
  //       }
  //     },
  //     {
  //       path: 'transfer',
  //       name: 'transfer',
  //       component: () => import('@/pages/module/transfer.vue'),
  //       meta: {
  //         title: '穿梭框',
  //         singles: true
  //       }
  //     }
  //   ]
  // }
]
